# Test script for multiple contracts payment
Write-Host "Testing Multiple Contracts Payment..." -ForegroundColor Cyan

# Test 1: Health Check
Write-Host "`n1. Testing Health Check..." -ForegroundColor Yellow
try {
    $health = Invoke-RestMethod -Uri "http://localhost:8084/actuator/health" -Method Get
    Write-Host "Health Check: $($health.status)" -ForegroundColor Green
} catch {
    Write-Host "Health Check Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Get Active Contracts for Customer 1
Write-Host "`n2. Getting active contracts for customer 1..." -ForegroundColor Yellow
try {
    $contracts = Invoke-RestMethod -Uri "http://localhost:8084/api/customer-payment/customer/1/active-contracts" -Method Get
    Write-Host "Found $($contracts.Count) active contracts" -ForegroundColor Green
    $contracts | ForEach-Object {
        $totalPaid = if ($_.totalPaid) { $_.totalPaid } else { 0 }
        $remaining = $_.totalAmount - $totalPaid
        Write-Host "   Contract #$($_.id): Total=$($_.totalAmount), Paid=$totalPaid, Remaining=$remaining"
    }
} catch {
    Write-Host "Failed to get contracts: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Continuing with test using predefined contract IDs..." -ForegroundColor Yellow
}

# Test 3: Create Multiple Contracts Payment
Write-Host "`n3. Creating multiple contracts payment..." -ForegroundColor Yellow

$requestBody = @{
    customerId = 1
    totalAmount = 50000000
    paymentMethod = 0
    note = "Test multiple contracts payment"
    contractPayments = @(
        @{
            contractId = 7
            allocatedAmount = 15000000
        },
        @{
            contractId = 9
            allocatedAmount = 24000000
        },
        @{
            contractId = 10
            allocatedAmount = 11000000
        }
    )
}

$jsonBody = $requestBody | ConvertTo-Json -Depth 3
Write-Host "Request Body:" -ForegroundColor Cyan
Write-Host $jsonBody

try {
    $payment = Invoke-RestMethod -Uri "http://localhost:8084/api/customer-payment/multiple-contracts" -Method Post -Body $jsonBody -ContentType "application/json"
    Write-Host "SUCCESS! Payment created with ID: $($payment.id)" -ForegroundColor Green
    Write-Host "Payment Details:" -ForegroundColor Cyan
    $payment | ConvertTo-Json -Depth 3

    # Test 4: Verify Contract Payments
    Write-Host "`n4. Verifying contract payments..." -ForegroundColor Yellow
    try {
        $contractPayments = Invoke-RestMethod -Uri "http://localhost:8084/api/customer-payment/payment/$($payment.id)/contract-payments" -Method Get
        Write-Host "Found $($contractPayments.Count) contract payments for payment ID $($payment.id)" -ForegroundColor Green
        $contractPayments | ForEach-Object {
            Write-Host "   Contract $($_.contractId): $($_.allocatedAmount) VND"
        }
    } catch {
        Write-Host "Failed to verify contract payments: $($_.Exception.Message)" -ForegroundColor Red
    }

} catch {
    Write-Host "❌ FAILED to create payment!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red

    if ($_.Exception.Response) {
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body: $responseBody" -ForegroundColor Red
        } catch {
            Write-Host "Could not read response body" -ForegroundColor Red
        }
    }
}

# Test 5: Error Case - Total Amount Mismatch
Write-Host "`n5. Testing error case - total amount mismatch..." -ForegroundColor Yellow

$errorRequestBody = @{
    customerId = 1
    totalAmount = 40000000  # Wrong total (should be 39000000)
    paymentMethod = 0
    note = "Test error case"
    contractPayments = @(
        @{
            contractId = 7
            allocatedAmount = 15000000
        },
        @{
            contractId = 9
            allocatedAmount = 24000000
        }
    )
}

$errorJsonBody = $errorRequestBody | ConvertTo-Json -Depth 3

try {
    $errorPayment = Invoke-RestMethod -Uri "http://localhost:8084/api/customer-payment/multiple-contracts" -Method Post -Body $errorJsonBody -ContentType "application/json"
    Write-Host "ERROR: This should have failed but didn't!" -ForegroundColor Red
} catch {
    Write-Host "Expected error caught: $($_.Exception.Message)" -ForegroundColor Green
}

Write-Host "`nTest completed!" -ForegroundColor Cyan
